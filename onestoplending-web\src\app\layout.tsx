import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import <PERSON>rip<PERSON> from "next/script";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  metadataBase: new URL('https://onestoplendingcanada.ca'),
  title: "One Stop Lending - Flexible Financing Solutions | Easy Payment Plans",
  description: "Get instant financing for anything you need. From medical procedures to personal purchases - simple, fast, and transparent payment plans with competitive rates.",
  keywords: "personal financing, flexible payment plans, instant approval, medical financing, purchase financing, easy loans, payment solutions, financing options",
  authors: [{ name: "One Stop Lending" }],
  creator: "One Stop Lending",
  publisher: "One Stop Lending",
  robots: "index, follow",
  viewport: "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://onestoplendingcanada.ca",
    title: "One Stop Lending - Flexible Financing Solutions",
    description: "Get instant financing for anything you need. From medical procedures to personal purchases - simple, fast, and transparent payment plans.",
    siteName: "One Stop Lending",
    images: [
      {
        url: "/logo.png",
        width: 1200,
        height: 630,
        alt: "One Stop Lending Logo",
      },
    ],
  },

  twitter: {
    card: "summary_large_image",
    title: "One Stop Lending - Flexible Financing Solutions",
    description: "Get instant financing for anything you need. From medical procedures to personal purchases.",
    images: ["/logo.png"],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'GA_MEASUREMENT_ID');
          `}
        </Script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
