'use client';

import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';

const features = [
  {
    icon: '💳',
    title: 'Flexible Payment Plans',
    description: 'Customizable payment schedules that fit your budget, from 6 to 60 months.',
  },
  {
    icon: '⚡',
    title: 'Instant Approval',
    description: 'Get approval decisions in under 24 hours with our streamlined application process.',
  },
  {
    icon: '🔒',
    title: 'Secure & Transparent',
    description: 'Bank-level security with transparent pricing - no hidden fees or surprises.',
  },
  {
    icon: '📱',
    title: 'Easy Application',
    description: 'Simple online application process that takes just minutes to complete.',
  },
  {
    icon: '💰',
    title: 'Competitive Rates',
    description: 'Fair and competitive rates that make financing accessible for everyone.',
  },
  {
    icon: '📊',
    title: 'Wide Range',
    description: 'Finance anything from $100 to $50,000 - medical procedures, purchases, and more.',
  },
];

export default function Features() {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  return (
    <section id="features" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Why Choose <span className="text-onestop-blue">One Stop Lending</span>?
          </h2>
          <p className="text-xl text-onestop-gray-dark max-w-3xl mx-auto">
            We provide comprehensive financing solutions for all your needs with transparent pricing and instant approvals.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{
                scale: 1.05,
                transition: { duration: 0.2 }
              }}
              className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
                className="text-4xl mb-4"
              >
                {feature.icon}
              </motion.div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-onestop-gray-dark leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-onestop-blue to-onestop-blue-light p-8 rounded-2xl text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Ready to Get Financing?
            </h3>
            <p className="text-xl mb-6 opacity-90">
              Join thousands of satisfied customers who trust One Stop Lending for their financing needs.
            </p>
            <motion.a
              href="#contact"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="inline-block bg-white text-onestop-blue px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-200"
            >
              Get Financing
            </motion.a>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
