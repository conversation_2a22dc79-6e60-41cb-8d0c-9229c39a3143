{"..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "app\\page.tsx -> @/components/Contact": {"id": "app\\page.tsx -> @/components/Contact", "files": ["static/chunks/_app-pages-browser_src_components_Contact_tsx.js"]}, "app\\page.tsx -> @/components/Features": {"id": "app\\page.tsx -> @/components/Features", "files": ["static/chunks/_app-pages-browser_src_components_Features_tsx.js"]}, "app\\page.tsx -> @/components/Footer": {"id": "app\\page.tsx -> @/components/Footer", "files": ["static/chunks/_app-pages-browser_src_components_Footer_tsx.js"]}, "app\\page.tsx -> @/components/HowItWorks": {"id": "app\\page.tsx -> @/components/HowItWorks", "files": ["static/chunks/_app-pages-browser_src_components_HowItWorks_tsx.js"]}, "app\\page.tsx -> @/components/Newsletter": {"id": "app\\page.tsx -> @/components/Newsletter", "files": ["static/chunks/_app-pages-browser_src_components_Newsletter_tsx.js"]}}