'use client';

import { motion } from 'framer-motion';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function Privacy() {
  return (
    <div className="min-h-screen">
      <Header />
      
      <section className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Privacy Policy
            </h1>
            <p className="text-lg text-surgiflex-gray-dark mb-8">
              Last updated: January 2025
            </p>

            <div className="prose prose-lg max-w-none">
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.1 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">1. Information We Collect</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  We collect information you provide directly to us, such as when you create an account, 
                  request a demo, subscribe to our newsletter, or contact us for support.
                </p>
                <ul className="list-disc pl-6 text-surgiflex-gray-dark space-y-2">
                  <li>Personal information (name, email address, phone number)</li>
                  <li>Practice information (clinic name, address, specialty)</li>
                  <li>Communication preferences</li>
                  <li>Usage data and analytics</li>
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">2. How We Use Your Information</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  We use the information we collect to provide, maintain, and improve our services:
                </p>
                <ul className="list-disc pl-6 text-surgiflex-gray-dark space-y-2">
                  <li>To provide and deliver our financing services</li>
                  <li>To send you technical notices and support messages</li>
                  <li>To communicate with you about products, services, and events</li>
                  <li>To monitor and analyze trends and usage</li>
                  <li>To comply with legal obligations</li>
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.3 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">3. Information Sharing</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  We do not sell, trade, or otherwise transfer your personal information to third parties 
                  without your consent, except as described in this policy:
                </p>
                <ul className="list-disc pl-6 text-surgiflex-gray-dark space-y-2">
                  <li>With your explicit consent</li>
                  <li>To comply with legal requirements</li>
                  <li>To protect our rights and safety</li>
                  <li>With trusted service providers who assist our operations</li>
                </ul>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">4. Data Security</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  We implement appropriate security measures to protect your personal information 
                  against unauthorized access, alteration, disclosure, or destruction.
                </p>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.5 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">5. Your Rights</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  You have the right to access, update, or delete your personal information. 
                  You may also opt out of certain communications from us.
                </p>
              </motion.section>

              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="mb-8"
              >
                <h2 className="text-2xl font-bold text-gray-900 mb-4">6. Contact Us</h2>
                <p className="text-surgiflex-gray-dark mb-4">
                  If you have any questions about this Privacy Policy, please contact us:
                </p>
                <div className="bg-surgiflex-gray p-6 rounded-lg">
                  <p className="text-surgiflex-gray-dark">
                    <strong>Email:</strong> <a href="mailto:<EMAIL>" className="text-surgiflex-blue hover:underline"><EMAIL></a><br />
                    <strong>Phone:</strong> <a href="tel:+14034022015" className="text-surgiflex-blue hover:underline">+14034022015</a><br />
                    <strong>Address:</strong> 40 Hopewell Way NE #10, Calgary, AB T3J 5H7
                  </p>
                </div>
              </motion.section>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
