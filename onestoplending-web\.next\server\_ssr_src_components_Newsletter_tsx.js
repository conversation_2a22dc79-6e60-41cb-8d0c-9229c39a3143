"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_Newsletter_tsx";
exports.ids = ["_ssr_src_components_Newsletter_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/Newsletter.tsx":
/*!***************************************!*\
  !*** ./src/components/Newsletter.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Newsletter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Newsletter() {\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) return;\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            // Simulate API call - replace with actual newsletter service\n            await new Promise((resolve)=>setTimeout(resolve, 1500));\n            console.log('Newsletter signup:', email);\n            setSubmitStatus('success');\n            setEmail('');\n        } catch (error) {\n            console.error('Newsletter signup error:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-gradient-to-r from-onestop-blue to-onestop-blue-dark py-16\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-3xl mx-auto text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    whileInView: {\n                        opacity: 1,\n                        y: 0\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    viewport: {\n                        once: true\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                            children: \"Stay Updated with One Stop Lending\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-blue-100 mb-8\",\n                            children: \"Get the latest updates on healthcare financing trends, new features, and industry insights delivered to your inbox.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Enter your email address\",\n                                        required: true,\n                                        disabled: isSubmitting,\n                                        className: \"w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-surgiflex-blue transition-all duration-200\",\n                                        \"aria-label\": \"Email address for newsletter subscription\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.button, {\n                                    type: \"submit\",\n                                    disabled: isSubmitting || !email,\n                                    whileHover: {\n                                        scale: isSubmitting ? 1 : 1.05\n                                    },\n                                    whileTap: {\n                                        scale: isSubmitting ? 1 : 0.95\n                                    },\n                                    className: `px-6 py-3 rounded-lg font-semibold transition-all duration-200 ${isSubmitting || !email ? 'bg-gray-300 cursor-not-allowed text-gray-500' : 'bg-white text-surgiflex-blue hover:bg-gray-50 shadow-lg hover:shadow-xl'}`,\n                                    children: isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"animate-spin -ml-1 mr-2 h-4 w-4\",\n                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                fill: \"none\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                        className: \"opacity-25\",\n                                                        cx: \"12\",\n                                                        cy: \"12\",\n                                                        r: \"10\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                                        lineNumber: 90,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        className: \"opacity-75\",\n                                                        fill: \"currentColor\",\n                                                        d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                                        lineNumber: 98,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 21\n                                            }, this),\n                                            \"Subscribing...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 19\n                                    }, this) : 'Subscribe'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, this),\n                        submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"mt-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg\",\n                            children: \"✅ Thank you for subscribing! You'll receive our latest updates and insights.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 15\n                        }, this),\n                        submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                y: 10\n                            },\n                            animate: {\n                                opacity: 1,\n                                y: 0\n                            },\n                            className: \"mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg\",\n                            children: \"❌ Sorry, there was an error subscribing. Please try again later.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-blue-200 text-sm mt-4\",\n                            children: \"We respect your privacy. Unsubscribe at any time.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\Newsletter.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Newsletter.tsx\n");

/***/ })

};
;