"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_HowItWorks_tsx"],{

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   inView: () => (/* binding */ inView)\n/* harmony export */ });\n/* harmony import */ var motion_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! motion-dom */ \"(app-pages-browser)/./node_modules/motion-dom/dist/es/index.mjs\");\n\n\nconst thresholds = {\n    some: 0,\n    all: 1,\n};\nfunction inView(elementOrSelector, onStart, { root, margin: rootMargin, amount = \"some\" } = {}) {\n    const elements = (0,motion_dom__WEBPACK_IMPORTED_MODULE_0__.resolveElements)(elementOrSelector);\n    const activeIntersections = new WeakMap();\n    const onIntersectionChange = (entries) => {\n        entries.forEach((entry) => {\n            const onEnd = activeIntersections.get(entry.target);\n            /**\n             * If there's no change to the intersection, we don't need to\n             * do anything here.\n             */\n            if (entry.isIntersecting === Boolean(onEnd))\n                return;\n            if (entry.isIntersecting) {\n                const newOnEnd = onStart(entry);\n                if (typeof newOnEnd === \"function\") {\n                    activeIntersections.set(entry.target, newOnEnd);\n                }\n                else {\n                    observer.unobserve(entry.target);\n                }\n            }\n            else if (typeof onEnd === \"function\") {\n                onEnd(entry);\n                activeIntersections.delete(entry.target);\n            }\n        });\n    };\n    const observer = new IntersectionObserver(onIntersectionChange, {\n        root,\n        rootMargin,\n        threshold: typeof amount === \"number\" ? amount : thresholds[amount],\n    });\n    elements.forEach((element) => observer.observe(element));\n    return () => observer.disconnect();\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/framer-motion/dist/es/utils/use-in-view.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../render/dom/viewport/index.mjs */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/dom/viewport/index.mjs\");\n\n\n\nfunction useInView(ref, { root, margin, amount, once = false } = {}) {\n    const [isInView, setInView] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ref.current || (once && isInView))\n            return;\n        const onEnter = () => {\n            setInView(true);\n            return once ? undefined : () => setInView(false);\n        };\n        const options = {\n            root: (root && root.current) || undefined,\n            margin,\n            amount,\n        };\n        return (0,_render_dom_viewport_index_mjs__WEBPACK_IMPORTED_MODULE_1__.inView)(ref.current, onEnter, options);\n    }, [root, ref, margin, once, amount]);\n    return isInView;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9mcmFtZXItbW90aW9uL2Rpc3QvZXMvdXRpbHMvdXNlLWluLXZpZXcubWpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE0QztBQUNjOztBQUUxRCwwQkFBMEIscUNBQXFDLElBQUk7QUFDbkUsa0NBQWtDLCtDQUFRO0FBQzFDLElBQUksZ0RBQVM7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxzRUFBTTtBQUNyQixLQUFLO0FBQ0w7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZ3Rtb3RcXERvY3VtZW50c1xcR2l0SHViXFxPbmVTdG9wTGVuZGluZ1dlYnNpdGVcXG9uZXN0b3BsZW5kaW5nLXdlYlxcbm9kZV9tb2R1bGVzXFxmcmFtZXItbW90aW9uXFxkaXN0XFxlc1xcdXRpbHNcXHVzZS1pbi12aWV3Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgaW5WaWV3IH0gZnJvbSAnLi4vcmVuZGVyL2RvbS92aWV3cG9ydC9pbmRleC5tanMnO1xuXG5mdW5jdGlvbiB1c2VJblZpZXcocmVmLCB7IHJvb3QsIG1hcmdpbiwgYW1vdW50LCBvbmNlID0gZmFsc2UgfSA9IHt9KSB7XG4gICAgY29uc3QgW2lzSW5WaWV3LCBzZXRJblZpZXddID0gdXNlU3RhdGUoZmFsc2UpO1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmICghcmVmLmN1cnJlbnQgfHwgKG9uY2UgJiYgaXNJblZpZXcpKVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICBjb25zdCBvbkVudGVyID0gKCkgPT4ge1xuICAgICAgICAgICAgc2V0SW5WaWV3KHRydWUpO1xuICAgICAgICAgICAgcmV0dXJuIG9uY2UgPyB1bmRlZmluZWQgOiAoKSA9PiBzZXRJblZpZXcoZmFsc2UpO1xuICAgICAgICB9O1xuICAgICAgICBjb25zdCBvcHRpb25zID0ge1xuICAgICAgICAgICAgcm9vdDogKHJvb3QgJiYgcm9vdC5jdXJyZW50KSB8fCB1bmRlZmluZWQsXG4gICAgICAgICAgICBtYXJnaW4sXG4gICAgICAgICAgICBhbW91bnQsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiBpblZpZXcocmVmLmN1cnJlbnQsIG9uRW50ZXIsIG9wdGlvbnMpO1xuICAgIH0sIFtyb290LCByZWYsIG1hcmdpbiwgb25jZSwgYW1vdW50XSk7XG4gICAgcmV0dXJuIGlzSW5WaWV3O1xufVxuXG5leHBvcnQgeyB1c2VJblZpZXcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/HowItWorks.tsx":
/*!***************************************!*\
  !*** ./src/components/HowItWorks.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ HowItWorks)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/utils/use-in-view.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst steps = [\n    {\n        number: '01',\n        title: 'Simple Application',\n        description: 'Fill out our simple online application in under 3 minutes from any device.',\n        icon: '📝'\n    },\n    {\n        number: '02',\n        title: 'Quick Approval',\n        description: 'Get approved in under 24 hours with transparent terms and no hidden fees.',\n        icon: '⚡'\n    },\n    {\n        number: '03',\n        title: 'Get Your Funds',\n        description: 'Once approved, get your financing and make your purchase with confidence.',\n        icon: '💰'\n    }\n];\nfunction HowItWorks() {\n    _s();\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const isInView = (0,framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView)(ref, {\n        once: true,\n        margin: \"-100px\"\n    });\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        visible: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.3\n            }\n        }\n    };\n    const stepVariants = {\n        hidden: {\n            opacity: 0,\n            x: -50\n        },\n        visible: {\n            opacity: 1,\n            x: 0,\n            transition: {\n                duration: 0.8,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        id: \"how-it-works\",\n        className: \"py-20 bg-onestop-gray\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    ref: ref,\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8\n                    },\n                    className: \"text-center mb-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"How \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-onestop-blue\",\n                                    children: \"One Stop Lending\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 17\n                                }, this),\n                                \" Works\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-onestop-gray-dark max-w-3xl mx-auto\",\n                            children: \"Our streamlined process makes it easy to get the financing you need for anything.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    variants: containerVariants,\n                    initial: \"hidden\",\n                    animate: isInView ? \"visible\" : \"hidden\",\n                    className: \"space-y-16\",\n                    children: steps.map((step, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                            variants: stepVariants,\n                            className: \"flex flex-col lg:flex-row items-center gap-8 \".concat(index % 2 === 1 ? 'lg:flex-row-reverse' : ''),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 text-center lg:text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                            whileHover: {\n                                                scale: 1.05\n                                            },\n                                            className: \"inline-block bg-onestop-blue text-white text-2xl font-bold px-6 py-3 rounded-full mb-4\",\n                                            children: step.number\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-4\",\n                                            children: step.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-onestop-gray-dark leading-relaxed\",\n                                            children: step.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        whileHover: {\n                                            scale: 1.1,\n                                            rotate: 5\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        className: \"w-64 h-64 bg-white rounded-2xl shadow-lg flex items-center justify-center text-8xl\",\n                                        children: step.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                    initial: {\n                        opacity: 0,\n                        y: 30\n                    },\n                    animate: isInView ? {\n                        opacity: 1,\n                        y: 0\n                    } : {\n                        opacity: 0,\n                        y: 30\n                    },\n                    transition: {\n                        duration: 0.8,\n                        delay: 0.8\n                    },\n                    className: \"mt-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-8 rounded-2xl shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-center text-gray-900 mb-8\",\n                                children: \"Complete Process in Under 5 Minutes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0 md:space-x-4\",\n                                children: [\n                                    'Apply',\n                                    'Review',\n                                    'Approve',\n                                    'Schedule'\n                                ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        animate: isInView ? {\n                                            opacity: 1,\n                                            scale: 1\n                                        } : {\n                                            opacity: 0,\n                                            scale: 0.8\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: index * 0.2\n                                        },\n                                        className: \"flex flex-col items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 bg-onestop-blue text-white rounded-full flex items-center justify-center font-bold text-lg mb-2\",\n                                                children: index + 1\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-onestop-gray-dark font-medium\",\n                                                children: item\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this),\n                                            index < 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.motion.div, {\n                                                initial: {\n                                                    scaleX: 0\n                                                },\n                                                animate: isInView ? {\n                                                    scaleX: 1\n                                                } : {\n                                                    scaleX: 0\n                                                },\n                                                transition: {\n                                                    duration: 0.8,\n                                                    delay: (index + 1) * 0.3\n                                                },\n                                                className: \"hidden md:block w-20 h-1 bg-onestop-blue-light mt-4 origin-left\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                        lineNumber: 123,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\OneStopLendingWebsite\\\\onestoplending-web\\\\src\\\\components\\\\HowItWorks.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, this);\n}\n_s(HowItWorks, \"DljcBprJKYjULUac3YKdUV9OwZQ=\", false, function() {\n    return [\n        framer_motion__WEBPACK_IMPORTED_MODULE_2__.useInView\n    ];\n});\n_c = HowItWorks;\nvar _c;\n$RefreshReg$(_c, \"HowItWorks\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/HowItWorks.tsx\n"));

/***/ })

}]);